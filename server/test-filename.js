// 测试文件名处理逻辑
const path = require('path');

function testFilenameProcessing(originalName) {
  console.log(`\n测试文件名: "${originalName}"`);
  console.log(`原始长度: ${originalName.length}`);
  
  const ext = path.extname(originalName);
  let baseName = path.basename(originalName, ext);
  
  // 限制文件名长度，避免过长的文件名导致问题
  const maxBaseNameLength = 50; // 限制基础文件名最大长度
  if (baseName.length > maxBaseNameLength) {
    // 截取前面部分，保留一些原始信息
    baseName = baseName.substring(0, maxBaseNameLength);
    console.log(`文件名过长，已截取为: ${baseName}`);
  }

  // 移除可能导致问题的特殊字符
  baseName = baseName.replace(/[<>:"/\\|?*]/g, '_');
  
  const timestamp = Date.now();
  const uniqueName = `${baseName}_${timestamp}${ext}`;
  
  // 确保最终文件名不超过系统限制（通常为255字符）
  const maxTotalLength = 200; // 保守设置为200字符
  if (uniqueName.length > maxTotalLength) {
    const availableLength = maxTotalLength - `_${timestamp}${ext}`.length;
    const truncatedBaseName = baseName.substring(0, Math.max(1, availableLength));
    const finalName = `${truncatedBaseName}_${timestamp}${ext}`;
    console.log(`最终文件名截断: ${uniqueName} -> ${finalName}`);
    console.log(`最终长度: ${finalName.length}`);
    return finalName;
  } else {
    console.log(`生成的文件名: ${uniqueName}`);
    console.log(`最终长度: ${uniqueName.length}`);
    return uniqueName;
  }
}

// 测试各种文件名
const testCases = [
  'normal.mp4',
  'AI新手村_1l1621iuql7uz_#coze工作流_#coze图像流_#生成视频_#短视频制作_#coze教程_Coze工.mp4',
  '这是一个非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的文件名.mp4',
  'file<with>special:characters"/\\|?*.mp4',
  '短文件名.mp4'
];

testCases.forEach(testCase => {
  testFilenameProcessing(testCase);
});
