const fs = require('fs');
const path = require('path');

// 检查uploads目录中的所有文件
const uploadsDir = path.join(__dirname, 'uploads');

console.log('检查uploads目录:', uploadsDir);

if (fs.existsSync(uploadsDir)) {
  const files = fs.readdirSync(uploadsDir);
  console.log('找到的文件:');
  
  files.forEach(file => {
    const filePath = path.join(uploadsDir, file);
    const stats = fs.statSync(filePath);
    
    console.log(`文件: ${file}`);
    console.log(`  大小: ${stats.size} bytes`);
    console.log(`  文件名长度: ${file.length} 字符`);
    console.log(`  是否可读: ${fs.access ? 'checking...' : 'unknown'}`);
    
    // 检查文件是否可读
    try {
      fs.accessSync(filePath, fs.constants.R_OK);
      console.log(`  访问状态: ✅ 可读`);
    } catch (err) {
      console.log(`  访问状态: ❌ 不可读 - ${err.message}`);
    }
    
    console.log('---');
  });
} else {
  console.log('uploads目录不存在');
}
