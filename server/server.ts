import speechRouter from './routes/speechRecognitionRouter';
import configRouter from './routes/configRouter';
import uploadRouter from './routes/uploadRouter';
import knowledgeBaseRouter from './routes/knowledgeBaseRouter';
import aliVocabularyRouter from './routes/aliVocabularyRouter';

// 注册路由
app.use('/api/speech', speechRouter);
app.use('/api/config', configRouter);
app.use('/api/upload', uploadRouter);
app.use('/api/knowledge-base', knowledgeBaseRouter);
app.use('/api/ali-vocabulary', aliVocabularyRouter); 