/**
 * 阿里云语音识别热词管理服务
 * 提供热词表的创建、查询、更新和删除功能
 */

import axios from 'axios';
import { API_BASE_URL } from '../config/constants';

export interface Hotword {
  name: string;
  weight: number;
  lang?: string;
}

export interface CreateVocabularyRequest {
  apiKey: string;
  prefix: string;
  vocabulary: Hotword[];
  targetModel?: string;
}

export interface ListVocabularyRequest {
  apiKey: string;
  prefix?: string;
  pageIndex?: number;
  pageSize?: number;
}

export interface QueryVocabularyRequest {
  apiKey: string;
  vocabularyId: string;
}

export interface UpdateVocabularyRequest {
  apiKey: string;
  vocabularyId: string;
  vocabulary: Hotword[];
}

export interface DeleteVocabularyRequest {
  apiKey: string;
  vocabularyId: string;
}

export interface VocabularyListItem {
  vocabulary_id: string;
  prefix: string;
  created_at: string;
}

export interface VocabularyResponse {
  task_id: string;
  request_id: string;
  output: {
    vocabularies?: VocabularyListItem[];
    total?: number;
    vocabulary?: Hotword[];
    vocabulary_id?: string;
    prefix?: string;
    target_model?: string;
    created_at?: string;
    updated_at?: string;
  };
}

export interface VocabularyDetail {
  vocabulary_id: string;
  prefix: string;
  target_model: string;
  created_at: string;
  updated_at: string;
  vocabulary: Hotword[];
}

export interface TaskStatus {
  task_id: string;
  status: 'PENDING' | 'RUNNING' | 'SUCCEEDED' | 'FAILED';
  output?: any;
  created_at: string;
  updated_at: string;
}

export class AliVocabularyService {
  private static instance: AliVocabularyService;
  private baseUrl: string;

  private constructor() {
    this.baseUrl = `${API_BASE_URL}/ali-vocabulary`;
  }

  /**
   * 获取实例（单例模式）
   */
  public static getInstance(): AliVocabularyService {
    if (!AliVocabularyService.instance) {
      AliVocabularyService.instance = new AliVocabularyService();
    }
    return AliVocabularyService.instance;
  }

  /**
   * 创建热词表
   */
  async createVocabulary(params: CreateVocabularyRequest): Promise<VocabularyResponse> {
    try {
      const response = await axios.post(`${this.baseUrl}/create`, params);
      return response.data;
    } catch (error: any) {
      console.error('创建热词表出错:', error);
      throw error;
    }
  }

  /**
   * 检查任务状态
   */
  async checkTaskStatus(apiKey: string, taskId: string): Promise<TaskStatus> {
    try {
      const response = await axios.get(`${this.baseUrl}/tasks/${taskId}`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      });
      return response.data;
    } catch (error: any) {
      console.error('检查任务状态出错:', error);
      throw error;
    }
  }

  /**
   * 列出热词表
   */
  async listVocabularies(params: ListVocabularyRequest): Promise<VocabularyResponse> {
    try {
      const response = await axios.post(`${this.baseUrl}/list`, params);
      return response.data;
    } catch (error: any) {
      console.error('列出热词表出错:', error);
      throw error;
    }
  }

  /**
   * 查询热词表详情
   */
  async queryVocabulary(params: QueryVocabularyRequest): Promise<VocabularyResponse> {
    try {
      const response = await axios.post(`${this.baseUrl}/query`, params);
      return response.data;
    } catch (error: any) {
      console.error('查询热词表详情出错:', error);
      throw error;
    }
  }

  /**
   * 更新热词表
   */
  async updateVocabulary(params: UpdateVocabularyRequest): Promise<VocabularyResponse> {
    try {
      const response = await axios.post(`${this.baseUrl}/update`, params);
      return response.data;
    } catch (error: any) {
      console.error('更新热词表出错:', error);
      throw error;
    }
  }

  /**
   * 删除热词表
   */
  async deleteVocabulary(params: DeleteVocabularyRequest): Promise<VocabularyResponse> {
    try {
      const response = await axios.post(`${this.baseUrl}/delete`, params);
      return response.data;
    } catch (error: any) {
      console.error('删除热词表出错:', error);
      throw error;
    }
  }
} 