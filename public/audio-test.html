<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频上下文测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.good {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .primary {
            background-color: #007bff;
            color: white;
        }
        .success {
            background-color: #28a745;
            color: white;
        }
        .warning {
            background-color: #ffc107;
            color: black;
        }
        .danger {
            background-color: #dc3545;
            color: white;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>音频上下文状态测试</h1>
    
    <div id="audioContextStatus" class="status warning">
        音频上下文状态: 未初始化
    </div>
    
    <div id="microphoneStatus" class="status warning">
        麦克风状态: 未检测
    </div>
    
    <div>
        <button id="initAudio" class="primary">初始化音频</button>
        <button id="resumeAudio" class="success">恢复音频上下文</button>
        <button id="suspendAudio" class="warning">暂停音频上下文</button>
        <button id="closeAudio" class="danger">关闭音频上下文</button>
    </div>
    
    <div>
        <button id="startRecording" class="success">开始录音</button>
        <button id="stopRecording" class="danger">停止录音</button>
        <button id="checkHealth" class="primary">检查健康状态</button>
    </div>
    
    <h3>日志输出:</h3>
    <div id="log"></div>
    
    <script>
        let audioContext = null;
        let mediaStream = null;
        let isRecording = false;
        
        const log = (message) => {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        };
        
        const updateStatus = () => {
            const audioStatus = document.getElementById('audioContextStatus');
            const micStatus = document.getElementById('microphoneStatus');
            
            if (audioContext) {
                audioStatus.textContent = `音频上下文状态: ${audioContext.state}`;
                audioStatus.className = `status ${audioContext.state === 'running' ? 'good' : 'warning'}`;
            } else {
                audioStatus.textContent = '音频上下文状态: 未初始化';
                audioStatus.className = 'status warning';
            }
            
            if (mediaStream) {
                const tracks = mediaStream.getAudioTracks();
                if (tracks.length > 0) {
                    const track = tracks[0];
                    micStatus.textContent = `麦克风状态: ${track.readyState} (${mediaStream.active ? '活跃' : '非活跃'})`;
                    micStatus.className = `status ${track.readyState === 'live' && mediaStream.active ? 'good' : 'warning'}`;
                } else {
                    micStatus.textContent = '麦克风状态: 无音频轨道';
                    micStatus.className = 'status error';
                }
            } else {
                micStatus.textContent = '麦克风状态: 未获取';
                micStatus.className = 'status warning';
            }
        };
        
        const initAudio = async () => {
            try {
                log('开始初始化音频...');
                
                // 创建音频上下文
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                log(`音频上下文已创建，状态: ${audioContext.state}`);
                
                // 获取麦克风权限
                mediaStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });
                log('麦克风权限获取成功');
                
                updateStatus();
                
                // 监听状态变化
                audioContext.addEventListener('statechange', () => {
                    log(`音频上下文状态变化: ${audioContext.state}`);
                    updateStatus();
                });
                
            } catch (error) {
                log(`初始化音频失败: ${error.message}`);
                updateStatus();
            }
        };
        
        const resumeAudio = async () => {
            if (!audioContext) {
                log('音频上下文未初始化');
                return;
            }
            
            try {
                if (audioContext.state === 'suspended') {
                    await audioContext.resume();
                    log('音频上下文已恢复');
                } else {
                    log(`音频上下文当前状态: ${audioContext.state}，无需恢复`);
                }
                updateStatus();
            } catch (error) {
                log(`恢复音频上下文失败: ${error.message}`);
            }
        };
        
        const suspendAudio = async () => {
            if (!audioContext) {
                log('音频上下文未初始化');
                return;
            }
            
            try {
                if (audioContext.state === 'running') {
                    await audioContext.suspend();
                    log('音频上下文已暂停');
                } else {
                    log(`音频上下文当前状态: ${audioContext.state}，无法暂停`);
                }
                updateStatus();
            } catch (error) {
                log(`暂停音频上下文失败: ${error.message}`);
            }
        };
        
        const closeAudio = async () => {
            if (audioContext) {
                try {
                    await audioContext.close();
                    log('音频上下文已关闭');
                    audioContext = null;
                } catch (error) {
                    log(`关闭音频上下文失败: ${error.message}`);
                }
            }
            
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                log('媒体流已停止');
                mediaStream = null;
            }
            
            updateStatus();
        };
        
        const checkHealth = () => {
            log('=== 健康状态检查 ===');
            
            if (audioContext) {
                log(`音频上下文状态: ${audioContext.state}`);
                log(`音频上下文采样率: ${audioContext.sampleRate}`);
            } else {
                log('音频上下文: 未初始化');
            }
            
            if (mediaStream) {
                log(`媒体流活跃状态: ${mediaStream.active}`);
                const tracks = mediaStream.getAudioTracks();
                tracks.forEach((track, index) => {
                    log(`音频轨道 ${index}: ${track.readyState}, 启用: ${track.enabled}`);
                });
            } else {
                log('媒体流: 未获取');
            }
            
            log('=== 检查完成 ===');
        };
        
        // 绑定事件
        document.getElementById('initAudio').addEventListener('click', initAudio);
        document.getElementById('resumeAudio').addEventListener('click', resumeAudio);
        document.getElementById('suspendAudio').addEventListener('click', suspendAudio);
        document.getElementById('closeAudio').addEventListener('click', closeAudio);
        document.getElementById('checkHealth').addEventListener('click', checkHealth);
        
        // 页面可见性变化监听
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                log('页面失去焦点');
            } else {
                log('页面重新获得焦点');
                setTimeout(() => {
                    checkHealth();
                }, 1000);
            }
        });
        
        // 窗口焦点变化监听
        window.addEventListener('focus', () => {
            log('窗口重新获得焦点');
        });
        
        window.addEventListener('blur', () => {
            log('窗口失去焦点');
        });
        
        // 初始状态更新
        updateStatus();
        log('页面加载完成，可以开始测试');
    </script>
</body>
</html>
